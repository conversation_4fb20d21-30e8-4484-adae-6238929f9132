export default function (data) {
  let question = {};

  // Image data
  question.imageId = data.imageId || null;
  question.imageUrl = data.imageUrl || "";
  
  // Click areas
  question.clickAreas = data.clickAreas || [];
  
  // First Click Test settings
  question.mobileDisplay = data.mobileDisplay || "width";
  question.minClicks = parseInt(data.minClicks) || 1;
  question.maxClicks = data.maxClicks ? parseInt(data.maxClicks) : null;
  question.displayTime = data.displayTime ? parseInt(data.displayTime) : null;
  question.buttonText = data.buttonText || "";
  question.allowClickCancel = data.allowClickCancel || false;
  
  // Standard options
  question.skipOption = data.skipOption || false;
  question.skipText = data.skipText || "";
  question.commentOption = data.commentOption || false;

  return question;
}
