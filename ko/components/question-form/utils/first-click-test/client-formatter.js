export default function (data, mode) {
  console.log("1-click-test: client-formatter start", data);

  // Parse click areas from JSON string
  let clickAreas = [];
  if (data.click_areas) {
    try {
      const areas = typeof data.click_areas === 'string'
        ? JSON.parse(data.click_areas)
        : data.click_areas;

      if (Array.isArray(areas)) {
        clickAreas = areas.map(area => ({
          name: area.name || "",
          x: parseFloat(area.x) || 0,
          y: parseFloat(area.y) || 0,
          width: parseFloat(area.width) || 0,
          height: parseFloat(area.height) || 0
        }));
      }
    } catch (e) {
      console.warn("Failed to parse click areas:", e);
      clickAreas = [];
    }
  }

  // Parse gallery data
  let gallery = [];
  if (data.gallery && Array.isArray(data.gallery)) {
    gallery = data.gallery.map((v, i) => {
      return {
        id: v.id,
        mediaId: v.id,
        description: v.description || "",
        preview: v.poster || v.url,
        url: v.url,
        position: 'position' in v ? v.position : i,
      };
    });
  }

  const firstClick = data.firstClick || {};

  const result = {
    // Gallery data (inherited from GalleryQuestion)
    gallery: gallery,

    // Legacy image data (for backward compatibility)
    imageId: data.image_file_id || null,
    imageUrl: data.image_url || "",
    imagePreview: data.image_url || "",
    clickAreas: clickAreas,

    // First Click Test settings
    mobileDisplay: firstClick.mobile_view === "1" ? "height" : "width",
    minClicks: parseInt(firstClick.min_clicks) || 1,
    maxClicks: firstClick.max_clicks ? parseInt(firstClick.max_clicks) : "",
    displayTime: firstClick.display_time ? parseInt(firstClick.display_time) : "",
    buttonText: firstClick.button_text || "",
    allowClickCancel: firstClick.allow_click_cancel === "1" || firstClick.allow_click_cancel === true,

    // Standard options
    skipOption: data.skip_option === "1" || data.skip_option === true,
    skipText: data.skip_text || "",
    commentOption: data.comment_option === "1" || data.comment_option === true,
  };

  console.log("1-click-test: client-formatter", result);

  return result;
}
