export default function (data, type = 'default') {
  // Parse click areas
  let clickAreas = [];
  if (data.clickAreas && Array.isArray(data.clickAreas)) {
    clickAreas = data.clickAreas.map(area => ({
      name: area.name || "",
      x: parseFloat(area.x) || 0,
      y: parseFloat(area.y) || 0,
      width: parseFloat(area.width) || 0,
      height: parseFloat(area.height) || 0
    }));
  }

  const result = {
    // Image data
    imageId: data.imageId || null,
    imageUrl: data.imageUrl || "",
    clickAreas: clickAreas,
    
    // First Click Test settings
    mobileDisplay: data.mobileDisplay || "width",
    minClicks: parseInt(data.minClicks) || 1,
    maxClicks: data.maxClicks ? parseInt(data.maxClicks) : null,
    displayTime: data.displayTime ? parseInt(data.displayTime) : null,
    buttonText: data.buttonText || "",
    allowClickCancel: data.allowClickCancel || false,
    
    // Standard options
    skipOption: data.skipOption || false,
    skipText: data.skipText || "",
    commentOption: data.commentOption || false,
  };

  // Add Vue-specific fields if needed
  if (type === 'vue') {
    result.hasImage = !!result.imageUrl;
    result.hasClickAreas = result.clickAreas.length > 0;
    result.hasTimeLimit = !!result.displayTime;
    result.hasClickLimit = !!result.maxClicks;
  }

  return result;
}
