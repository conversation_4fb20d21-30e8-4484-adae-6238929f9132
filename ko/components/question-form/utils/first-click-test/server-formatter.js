export default function (data) {
  let question = {};

  // Gallery data (new approach)
  question.enableGallery = data.galleryEnabled ? 1 : 0;
  question.gallery = data.gallery
    .filter((v) => v.mediaId)
    .map((v) => {
      return {
        id: v.mediaId,
        description: v.description || "",
      };
    });
  question.files = data.gallery.filter((v) => v.mediaId).map((v) => v.mediaId);

  // Legacy image data (for backward compatibility)
  question.image_file_id = data.imageId || null;
  question.image_url = data.imageUrl || "";

  // Click areas as JSON string
  question.click_areas = JSON.stringify(data.clickAreas || []);

  // First Click Test settings
  question.mobile_display = data.mobileDisplay || "width";
  question.min_clicks = parseInt(data.minClicks) || 1;
  question.max_clicks = data.maxClicks ? parseInt(data.maxClicks) : null;
  question.display_time = data.displayTime ? parseInt(data.displayTime) : null;
  question.button_text = data.buttonText || "";
  question.allow_click_cancel = data.allowClickCancel ? "1" : "0";

  // Standard options
  question.skip_option = data.skipOption ? "1" : "0";
  question.skip_text = data.skipText || "";
  question.comment_option = data.commentOption ? "1" : "0";

  // Clean up empty values
  Object.keys(question).forEach(key => {
    if (question[key] === "" && key !== "button_text" && key !== "skip_text") {
      question[key] = null;
    }
  });

  console.log("1-click-test: server-formatter", question);

  return question;
}
