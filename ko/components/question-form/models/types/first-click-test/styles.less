// First Click Test Question Styles

.first-click-test-upload-area {
  border: 2px dashed #e0e6ed;
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
  background-color: #f8f9fa;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover:not(.disabled) {
    border-color: #3f65f1;
    background-color: #f0f4ff;
  }

  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &.dnd-hover {
    border-color: #3f65f1;
    background-color: #f0f4ff;
    transform: scale(1.02);
  }
}

.first-click-test-upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;

  .f-icon {
    color: #6c757d;
  }
}

.first-click-test-upload-text {
  .first-click-test-upload-title {
    font-size: 16px;
    font-weight: 500;
    color: #495057;
    margin-bottom: 4px;
  }

  .first-click-test-upload-subtitle {
    font-size: 14px;
    color: #6c757d;
  }
}

.first-click-test-upload-formats {
  margin-top: 16px;
  color: #6c757d;
}

.first-click-test-image-preview {
  border: 1px solid #e0e6ed;
  border-radius: 8px;
  overflow: hidden;
  background-color: #fff;
}

.first-click-test-image-container {
  position: relative;
  display: inline-block;
  width: 100%;

  &:hover .first-click-test-image-overlay {
    opacity: 1;
  }
}

.first-click-test-image {
  max-width: 100%;
  max-height: 400px;
  width: auto;
  height: auto;
  display: block;
  margin: 0 auto;
}

.first-click-test-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;

  .f-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    padding: 6px 12px;

    .f-icon {
      width: 14px;
      height: 14px;
    }
  }
}

.first-click-test-areas-info {
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-top: 1px solid #e0e6ed;
  color: #6c757d;
}

// Responsive adjustments
@media (max-width: 768px) {
  .first-click-test-upload-area {
    padding: 30px 15px;
  }

  .first-click-test-image-overlay {
    flex-direction: column;
    gap: 8px;

    .f-btn {
      font-size: 11px;
      padding: 4px 8px;
    }
  }
}

// Form validation styles
.first-click-test-upload-area.has-error {
  border-color: #dc3545;
  background-color: #fff5f5;
}

// Loading state
.first-click-test-image-loading {
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 24px;
    height: 24px;
    margin: -12px 0 0 -12px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3f65f1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Dark theme support
.theme-dark {
  .first-click-test-upload-area {
    border-color: #495057;
    background-color: #343a40;
    color: #f8f9fa;

    &:hover:not(.disabled) {
      border-color: #3f65f1;
      background-color: #2c3034;
    }

    &.dnd-hover {
      background-color: #2c3034;
    }
  }

  .first-click-test-image-preview {
    border-color: #495057;
    background-color: #343a40;
  }

  .first-click-test-areas-info {
    background-color: #495057;
    border-color: #6c757d;
    color: #f8f9fa;
  }
}
